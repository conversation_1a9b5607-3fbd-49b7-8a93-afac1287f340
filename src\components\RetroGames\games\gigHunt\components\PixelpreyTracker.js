"use client";

import HUDContainer from './HUDContainer';

/**
 * PixelpreyTracker - Displays shot count for each pixelprey type with icons
 * Shows static pixelprey icons (no wings) with count numbers
 */
const PixelpreyTracker = ({ shotCounts = {} }) => {
  // Available pixelprey types with their icon paths
  const preyTypes = [
    { 
      type: 'illustrator', 
      icon: '/Projects/Games/gh/pixelprey/illustrator/illustrator_pixelprey.svg',
      name: 'AI'
    },
    { 
      type: 'ae', 
      icon: '/Projects/Games/gh/pixelprey/ae/ae_pixelprey.svg',
      name: 'AE'
    },
    { 
      type: 'blender', 
      icon: '/Projects/Games/gh/pixelprey/blender/blender_pixelprey.svg',
      name: 'BL'
    },
    { 
      type: 'netflix', 
      icon: '/Projects/Games/gh/pixelprey/netflix/nf_pixelprey.svg',
      name: 'NF'
    },
    { 
      type: 'reddit', 
      icon: '/Projects/Games/gh/pixelprey/reddit/rd_pixelprey.svg',
      name: 'RD'
    }
  ];

  return (
    <HUDContainer position="bottom-right" padding="8px 12px">
      <div className="flex flex-col gap-1">
        <div className="text-center mb-1" style={{ fontSize: '14px' }}>
          HUNTED
        </div>
        {preyTypes.map((prey) => (
          <div key={prey.type} className="flex items-center gap-2">
            <img
              src={prey.icon}
              alt={`${prey.type} pixelprey`}
              className="w-6 h-6"
              style={{
                imageRendering: 'pixelated', // Keep retro pixel look
                filter: 'brightness(1.2)' // Slightly brighter for visibility
              }}
            />
            <span style={{ fontSize: '14px', minWidth: '20px' }}>
              {shotCounts[prey.type] || 0}
            </span>
          </div>
        ))}
      </div>
    </HUDContainer>
  );
};

export default PixelpreyTracker;
