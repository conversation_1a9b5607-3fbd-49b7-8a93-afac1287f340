"use client";

import { useState, useEffect, useRef } from 'react';
import { motion, useAnimation } from 'framer-motion';
import Background from '../components/Background';
import SpriteAnimator from '../components/SpriteAnimator';
import PixelPreyManager from '../components/PixelPreyManager';
import LifeDisplay from '../components/LifeDisplay';
import HUDContainer from '../components/HUDContainer';
import PixelpreyTracker from '../components/PixelpreyTracker';

/**
 * GameplayScreen - Main gameplay screen for Gig Hunt
 * Handles the hunting gameplay, <PERSON>'s behavior, and PixelPrey spawning
 */
const GameplayScreen = ({ onBackToMenu, isTransitioning }) => {
  const [williamState, setWilliamState] = useState('idle');
  const [jumpFrame, setJumpFrame] = useState(1); // 1 or 2 for jump frames
  const [william<PERSON>idden, setWilliamHidden] = useState(false); // Track if <PERSON> is permanently hidden

  // Game state
  const [gameScore, setGameScore] = useState(0);
  const [gameKnowledge, setGameKnowledge] = useState(0);
  const [preySpawningActive, setPreySpawningActive] = useState(false);
  const [gameComplete, setGameComplete] = useState(false);

  // Life system state
  const [lives, setLives] = useState(3);
  const [maxLives] = useState(3);
  const [isLosingLife, setIsLosingLife] = useState(false);
  const [gameOver, setGameOver] = useState(false);

  // Pixelprey tracking state
  const [shotCounts, setShotCounts] = useState({
    illustrator: 0,
    ae: 0,
    blender: 0,
    netflix: 0,
    reddit: 0
  });

  // Life loss visual effect state
  const [showLifeLossEffect, setShowLifeLossEffect] = useState(false);

  const williamControls = useAnimation();
  const preyManagerRef = useRef(null);

  // William sprite configurations
  const williamSprites = {
    idle: {
      basePath: '/Projects/Games/gh/william/',
      frames: ['william_front_idle.svg'],
      frameRate: 1000 // Static frame
    },
    walking: {
      basePath: '/Projects/Games/gh/william/',
      frames: [
        'william_walk_right_01.svg',
        'william_walk_right_02.svg', 
        'william_walk_right_03.svg',
        'william_walk_right_04.svg'
      ],
      frameRate: 200
    },
    angry: {
      basePath: '/Projects/Games/gh/william/',
      frames: ['william_front_angry.svg'],
      frameRate: 1000
    },
    jumping: {
      basePath: '/Projects/Games/gh/william/',
      frames: [
        'william_jump_01.svg',
        'william_jump_02.svg'
      ],
      frameRate: 1000 // Static frame - no cycling
    }
  };

  // Test William animation cycle with movement and jumping
  useEffect(() => {
    const testSequence = async () => {
      // Start walking immediately from off-screen position
      setWilliamState('walking');

      // Walk across screen at consistent speed - straight line movement
      await williamControls.start({
        x: 400, // Move further across screen
        transition: { duration: 3, ease: "linear" } // Linear = consistent speed
      });

      // Stop and idle at the destination
      setWilliamState('idle');

      // Idle, then get angry
      await new Promise(resolve => setTimeout(resolve, 1000));
      setWilliamState('angry');

      // Stay angry briefly, then jump
      await new Promise(resolve => setTimeout(resolve, 800));
      setWilliamState('jumping');

      // Jump sequence with physics-like motion
      const jumpSequence = async () => {
        // Start jump with frame 1 (going up)
        setJumpFrame(1);

        await williamControls.start({
          y: -160, // Jump up 160px (even higher!)
          transition: { duration: 0.4, ease: "easeOut" }
        });

        // At peak: switch to frame 2 (smaller, distant) and apply mask
        setJumpFrame(2);

        // Stay at peak for a moment while behind grass
        await new Promise(resolve => setTimeout(resolve, 300));

        // Fall down
        await williamControls.start({
          y: 0, // Back to ground
          transition: { duration: 0.6, ease: "easeIn" }
        });

        // William is now permanently hidden behind grass
        setTimeout(() => {
          setWilliamHidden(true); // Keep William hidden permanently
          setJumpFrame(1); // Reset frame but keep mask active

          // Start prey spawning after a short delay
          setTimeout(() => {
            setPreySpawningActive(true);
          }, 1500); // 1.5 second delay before prey start spawning
        }, 100);
      };

      await jumpSequence();
      setWilliamState('idle');

      // William stays hidden after jumping - PixelPrey will start spawning
    };

    if (!isTransitioning) {
      testSequence();
    }
  }, [isTransitioning, williamControls]);

  // Handle score updates from PixelPreyManager
  const handleScoreUpdate = ({ score, knowledge, preyType }) => {
    setGameScore(score);
    setGameKnowledge(knowledge);

    // Update shot count for this prey type (only for safe prey that give points)
    if (preyType) {
      setShotCounts(prev => ({
        ...prev,
        [preyType]: prev[preyType] + 1
      }));
    }
  };

  // Handle life loss from dangerous pixelpreys
  const handleLifeLost = (preyType) => {
    console.log('Life lost! Starting life loss animation...');
    setIsLosingLife(true);
    setShowLifeLossEffect(true);

    // Track dangerous prey shots too (even though they don't give points)
    if (preyType) {
      setShotCounts(prev => ({
        ...prev,
        [preyType]: prev[preyType] + 1
      }));
    }

    setLives(prev => {
      const newLives = prev - 1;
      console.log(`Lives remaining: ${newLives}`);

      if (newLives <= 0) {
        setGameOver(true);
        console.log('Game Over - No lives remaining!');
      }

      return newLives;
    });

    // Hide life loss effect after a short duration
    setTimeout(() => {
      setShowLifeLossEffect(false);
    }, 800); // 800ms flash effect
  };

  // Handle life loss animation completion
  const handleLifeLossComplete = () => {
    console.log('Life loss animation complete');
    setIsLosingLife(false);

    if (gameOver) {
      // Game over - return to menu after delay
      setTimeout(() => {
        onBackToMenu();
      }, 2000);
    } else {
      // Resume game
      if (preyManagerRef.current) {
        preyManagerRef.current.resumeGame();
      }
    }
  };

  // Handle game completion
  const handleGameComplete = (gameStats) => {
    setGameComplete(true);
    console.log('Game Complete!', gameStats);
    // Could show end game screen or return to menu after delay
    setTimeout(() => {
      onBackToMenu();
    }, 3000); // Return to menu after 3 seconds
  };

  const currentSprite = williamSprites[williamState];

  // Get the correct frame for jumping state
  const getCurrentFrame = () => {
    if (williamState === 'jumping') {
      return [currentSprite.frames[jumpFrame - 1]]; // Use specific jump frame
    }
    return currentSprite.frames;
  };

  return (
    <div
      className="relative w-full h-full overflow-hidden"
      style={{
        cursor: 'url(/Projects/Games/gh/background/crosshair.svg) 16 16, crosshair'
      }}
    >
      {/* Background Layer with smooth slide transition */}
      <Background
        backgroundType="gameplay"
        isTransitioning={isTransitioning}
      />

      {/* Game Content Layer with mask */}
      <div
        className="game-content-layer relative w-full h-full"
        style={{
          // Apply mask when William is in jump frame 2, and keep it on if William is hidden
          clipPath: (jumpFrame === 2 || williamHidden) ? 'polygon(0 0, 100% 0, 100% 75%, 0 75%)' : 'none'
        }}
      >
        {/* William Character */}
        <div className="absolute bottom-16 z-50" style={{ left: '-100px' }}>
          <motion.div
            initial={{ x: 0, opacity: 1 }}
            animate={{
              x: isTransitioning ? 0 : 100, // Start William closer to screen edge
              opacity: isTransitioning ? 0 : 1
            }}
            transition={{ duration: 0.6, ease: "linear" }} // Faster, linear entrance
          >
            <motion.div
              animate={williamControls}
              className="relative"
            >
              <SpriteAnimator
                spriteBasePath={currentSprite.basePath}
                frameNames={getCurrentFrame()}
                autoPlay={williamState !== 'jumping' && getCurrentFrame().length > 1}
                frameRate={currentSprite.frameRate}
                loop={williamState !== 'jumping'}
                className={`${williamState === 'jumping' && jumpFrame === 2 ? 'w-20 h-20' : 'w-24 h-24'}`}
                alt={`William ${williamState} ${williamState === 'jumping' ? `frame ${jumpFrame}` : ''}`}
              />
            </motion.div>
          </motion.div>
        </div>

        {/* PixelPrey Manager - spawns and manages flying software icons */}
        <PixelPreyManager
          ref={preyManagerRef}
          isActive={preySpawningActive}
          onScoreUpdate={handleScoreUpdate}
          onGameComplete={handleGameComplete}
          onLifeLost={handleLifeLost}
        />

      </div>

      {/* Score Display - Bottom Left */}
      <HUDContainer position="bottom-left">
        <div>SCORE: {gameScore}</div>
        <div>KNOWLEDGE: {gameKnowledge}</div>
        {gameComplete && (
          <div style={{ color: '#ffff00' }}>GAME COMPLETE!</div>
        )}
        {gameOver && (
          <div style={{ color: '#ff4444' }}>GAME OVER!</div>
        )}
      </HUDContainer>

      {/* Life Display - Top Right */}
      <HUDContainer position="top-right">
        <LifeDisplay
          lives={lives}
          maxLives={maxLives}
          isLosingLife={isLosingLife}
          onLifeLost={handleLifeLossComplete}
        />
      </HUDContainer>

      {/* Pixelprey Tracker - Bottom Right */}
      <PixelpreyTracker shotCounts={shotCounts} />

      {/* Back to Menu Button (for testing) */}
      <div className="absolute top-4 right-4 z-60">
        <button
          onClick={onBackToMenu}
          className="px-4 py-2 bg-red-600 text-white border-2 border-red-400 hover:bg-red-700 transition-colors"
          style={{
            fontFamily: 'var(--font-retro)',
            fontSize: '16px'
          }}
        >
          BACK TO MENU
        </button>
      </div>

      {/* Life Loss Visual Effect */}
      {showLifeLossEffect && (
        <div
          className="absolute inset-0 z-70 pointer-events-none"
          style={{
            background: 'rgba(255, 255, 255, 0.8)',
            animation: 'lifeLossFlash 0.8s ease-out'
          }}
        />
      )}

      {/* CSS for life loss effect */}
      <style jsx>{`
        @keyframes lifeLossFlash {
          0% {
            background: rgba(255, 255, 255, 0.9);
            filter: saturate(0) brightness(2);
          }
          50% {
            background: rgba(255, 255, 255, 0.5);
            filter: saturate(0.3) brightness(1.5);
          }
          100% {
            background: rgba(255, 255, 255, 0);
            filter: saturate(1) brightness(1);
          }
        }
      `}</style>
    </div>
  );
};

export default GameplayScreen;
