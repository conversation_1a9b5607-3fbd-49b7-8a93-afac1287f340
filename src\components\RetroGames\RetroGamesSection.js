"use client";

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import GameConsole from './GameConsole';
import GameScreen from './GameScreen';
import Button from '../Button';
import { useArcade } from '../../contexts/ArcadeContext';

const RetroGamesSection = () => {
  const { isArcadeActive, enterArcade } = useArcade();
  const [selectedGame, setSelectedGame] = useState(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Available games
  const games = [
    {
      id: 'snake',
      title: 'Snake',
      description: 'Classic snake game where you eat food to grow longer while avoiding walls and yourself.',
      color: '#4ade80', // Green
      icon: '🐍',
      trivia: 'Originally created in 1976, <PERSON> became famous on Nokia phones in the late 90s. This version is built with React and Canvas API.'
    },
    {
      id: 'gighunt',
      title: 'Gig Hunt',
      description: 'Hunt software icons to gain knowledge points, then hunt projects to land the gig!',
      color: '#f59e0b', // Orange
      icon: '🎯',
      trivia: 'A creative twist on Duck Hunt! <PERSON> helps you hunt software knowledge and projects. Built with React and SVG sprites for that authentic retro feel.'
    },
    // Future games will be added here
    // {
    //   id: 'tetris',
    //   title: 'Tetris',
    //   description: 'Block puzzle game where you arrange falling pieces to clear lines.',
    //   color: '#3b82f6', // Blue
    //   icon: '🧩',
    //   trivia: 'Created by Alexey Pajitnov in 1984, Tetris is one of the most recognizable puzzle games ever made. Coming soon to this arcade!'
    // },
    // {
    //   id: 'duckhunt',
    //   title: 'Duck Hunt',
    //   description: 'Shoot the flying ducks before they escape off screen.',
    //   color: '#f59e0b', // Orange
    //   icon: '🦆',
    //   trivia: 'Released by Nintendo in 1984, Duck Hunt used a light gun accessory. This web version uses mouse clicks instead!'
    // }
  ];

  const handleGameSelect = (game) => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(game);
      setIsTransitioning(false);

      // Smooth scroll to center the game area
      setTimeout(() => {
        const gameArea = document.getElementById('game-area');
        if (gameArea) {
          gameArea.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
        }
      }, 100); // Small delay to ensure the game screen is rendered
    }, 300); // Faster transition duration
  };

  const handleBackToConsole = () => {
    setIsTransitioning(true);
    setTimeout(() => {
      setSelectedGame(null);
      setIsTransitioning(false);
    }, 300);
  };

  return (
    <section
      id="retro-games-section"
      className="bg-background min-h-screen flex items-center justify-center relative z-10"
    >
      {/* Container with natural spacing */}
      <div className="w-3/4 mx-auto">
        <AnimatePresence mode="wait">
          {!isArcadeActive ? (
            // Entry Card State - Central card with enter button
            <motion.div
              key="entry-card"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
              className="flex items-center justify-center"
              style={{ height: '70vh' }}
            >
              <div className="bg-primary rounded-3xl p-12 text-center max-w-2xl shadow-lg">
              <div className="space-y-6">
                <div className="flex items-center justify-center space-x-3 mb-6">
                  <span className="text-3xl animate-pulse">🎮</span>
                  <h2 className="font-heading font-extrabold text-secondary text-3xl lg:text-4xl">
                    Retro Arcade
                  </h2>
                  <span className="text-3xl animate-pulse">🕹️</span>
                </div>

                <p className="text-secondary text-lg leading-relaxed mb-6">
                  Care for a nostalgia break? Check out some classic games recreated with modern web tech for your enjoyment.
                </p>

                <p className="text-secondary/80 text-base mb-8">
                  Want to see a more interactive project? Step into the arcade!
                </p>

                <Button
                  variant="filled"
                  onClick={enterArcade}
                >
                  Enter Arcade
                </Button>
              </div>
              </div>
            </motion.div>
          ) : (
            // Active Arcade State - Different layouts for game selection vs playing
            selectedGame ? (
              // Game Playing Layout - Full screen like Projects section
              <motion.div
                key="game-playing"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
                className="fixed inset-0 flex items-center justify-center z-10"
              >
                {/* Main container - exact same as Projects */}
                <div className="w-9/10 h-screen flex">
                  {/* Game Text - Left Side (1/3 of container) - Same as Projects */}
                  <div className="w-1/3 h-full flex items-center pl-8 lg:pl-12 bg-primary">
                    <div className="w-full">
                      <motion.div
                        key="game-text"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
                      >
                        <h2 className="font-heading font-extrabold text-secondary text-3xl lg:text-5xl mb-6">
                          {selectedGame.icon} {selectedGame.title}
                        </h2>
                        <div className="space-y-4">
                          <div>
                            <h3 className="font-heading font-bold text-secondary text-lg mb-2">Game Info</h3>
                            <p className="text-secondary text-lg mb-4">{selectedGame.description}</p>
                          </div>
                          <div>
                            <h3 className="font-heading font-bold text-secondary text-lg mb-2">Controls</h3>
                            <p className="text-secondary/80 text-sm mb-4">
                              Use <span className="bg-secondary/20 px-2 py-1 rounded">WASD</span> or <span className="bg-secondary/20 px-2 py-1 rounded">Arrow Keys</span> to move
                            </p>
                          </div>
                          <div>
                            <h3 className="font-heading font-bold text-secondary text-lg mb-2">Trivia</h3>
                            <p className="text-secondary/80 text-sm">
                              {selectedGame.trivia || "Classic arcade game recreated with modern web technologies for your enjoyment!"}
                            </p>
                          </div>
                        </div>

                        {/* Back Button - Below content */}
                        <div className="mt-8">
                          <Button
                            variant="outline"
                            onClick={handleBackToConsole}
                            className="hover:bg-accent hover:text-white hover:border-accent text-sm px-4 py-2"
                          >
                            ← Back to Games
                          </Button>
                        </div>
                      </motion.div>
                    </div>
                  </div>

                  {/* Game Screen Area - Right Side (2/3 of container) - Same as Projects */}
                  <div className="w-2/3 h-full flex items-center pr-8 lg:pr-12 relative">
                    <div className="w-full h-full bg-background">
                      <motion.div
                        key="game-screen"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
                        className="w-full h-full flex items-center justify-center p-4"
                      >
                        {/* CRT Screen with exact same sizing as project cards */}
                        <div
                          className="crt-screen active bg-black rounded-3xl overflow-hidden shadow-2xl"
                          style={{
                            // Exact same responsive sizing as project cards
                            width: 'min(100%, calc((100vh - 8rem) * 4/3))',
                            height: 'min(calc(100vw * 0.9 * 0.67 * 3/4), calc(100vh - 8rem))',
                            aspectRatio: '4/3'
                          }}
                        >
                          <div className="w-full h-full relative crt-content">
                            <GameScreen
                              game={selectedGame}
                              onBack={handleBackToConsole}
                              isTransitioning={isTransitioning}
                            />
                          </div>
                        </div>
                      </motion.div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ) : (
              // Game Selection Layout - Original grid layout
              <motion.div
                key="game-selection"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
                className="grid grid-cols-1 lg:grid-cols-3 gap-8"
                style={{ height: '70vh' }}
              >
                {/* Game Text - Left Side (1 column) */}
                <div className="lg:col-span-1 flex items-center bg-primary rounded-2xl p-8">
                  <div className="w-full">
                    <motion.div
                      key="console-text"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
                    >
                      <h2 className="font-heading font-extrabold text-secondary text-3xl lg:text-4xl mb-8">
                        🎮 Retro Arcade
                      </h2>
                      <p className="text-secondary text-lg mb-8">
                        Take a nostalgia break! Classic games recreated with modern web tech for your enjoyment.
                      </p>
                      <div className="text-secondary/60 text-sm">
                        <p>Click on a game card to start playing →</p>
                      </div>
                    </motion.div>
                  </div>
                </div>

                {/* Game Area - Right Side (2 columns) */}
                <div className="lg:col-span-2 flex items-center justify-center bg-background rounded-2xl p-8" style={{ height: '70vh' }}>
                  <motion.div
                    key="console"
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
                    className="w-full flex items-center justify-center"
                  >
                    <GameConsole
                      games={games}
                      onGameSelect={handleGameSelect}
                      isTransitioning={isTransitioning}
                    />
                  </motion.div>
                </div>
              </motion.div>
            )
          )}
        </AnimatePresence>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
};

export default RetroGamesSection;
