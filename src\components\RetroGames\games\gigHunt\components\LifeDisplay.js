"use client";

import { useState, useEffect } from 'react';

/**
 * LifeDisplay - Shows hearts representing player lives with blinking animation
 * Handles life loss animation and displays current life count
 */
const LifeDisplay = ({ 
  lives = 3, 
  maxLives = 3, 
  onLifeLost = null, // Callback when life loss animation completes
  isLosingLife = false // Trigger for life loss animation
}) => {
  const [blinkingHeart, setBlinkingHeart] = useState(null); // Index of heart that's blinking
  const [displayLives, setDisplayLives] = useState(lives); // Lives currently displayed

  // Handle life loss animation
  useEffect(() => {
    if (isLosingLife && lives < displayLives) {
      const heartToRemove = lives; // Index of heart to remove (0-based)
      setBlinkingHeart(heartToRemove);
      
      // Blink animation duration
      const blinkDuration = 1500; // 1.5 seconds of blinking
      
      setTimeout(() => {
        setBlinkingHeart(null);
        setDisplayLives(lives); // Update display to match actual lives
        
        if (onLifeLost) {
          onLifeLost(); // Notify parent that animation is complete
        }
      }, blinkDuration);
    }
  }, [isLosingLife, lives, displayLives, onLifeLost]);

  // Reset display lives when lives prop changes (for game restart)
  useEffect(() => {
    if (lives > displayLives) {
      setDisplayLives(lives);
    }
  }, [lives, displayLives]);

  // Generate heart elements
  const renderHearts = () => {
    const hearts = [];
    
    for (let i = 0; i < maxLives; i++) {
      const isVisible = i < displayLives;
      const isBlinking = blinkingHeart === i;
      
      hearts.push(
        <div
          key={i}
          className={`inline-block mr-1 ${isVisible ? 'opacity-100' : 'opacity-30'} ${
            isBlinking ? 'animate-pulse' : ''
          }`}
          style={{
            animation: isBlinking ? 'heartBlink 0.2s infinite' : 'none'
          }}
        >
          <img
            src="/Projects/Games/gh/background/life.svg"
            alt="Life"
            className="w-6 h-6"
            style={{
              filter: isVisible ? 'none' : 'grayscale(100%)',
              imageRendering: 'pixelated' // Keep retro pixel look
            }}
          />
        </div>
      );
    }
    
    return hearts;
  };

  return (
    <div className="flex items-center">
      {renderHearts()}
      
      {/* CSS for heart blinking animation */}
      <style jsx>{`
        @keyframes heartBlink {
          0%, 50% { opacity: 1; }
          51%, 100% { opacity: 0.3; }
        }
      `}</style>
    </div>
  );
};

export default LifeDisplay;
