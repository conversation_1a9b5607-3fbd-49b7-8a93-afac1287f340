"use client";

import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import PixelPrey from './PixelPrey';

/**
 * PixelPreyManager - Manages spawning and tracking of multiple PixelPrey
 * Handles game progression, scoring, life system, and prey spawning logic
 */
const PixelPreyManager = forwardRef(({
  isActive = false,
  onScoreUpdate,
  onGameComplete,
  onLifeLost // New callback for life loss events
}, ref) => {
  const [activePrey, setActivePrey] = useState([]);
  const [score, setScore] = useState(0);
  const [gamePhase, setGamePhase] = useState('waiting'); // waiting, spawning, paused, complete
  const [preyCounter, setPreyCounter] = useState(0);
  const [isPaused, setIsPaused] = useState(false); // Game pause state

  // Available prey types - mix of safe and dangerous
  const preyTypes = ['illustrator', 'ae', 'blender', 'netflix', 'reddit'];

  // Game configuration - More challenging
  const TOTAL_PREY = 12; // Increased from 6 to 12 for more challenge
  const SPAWN_INTERVAL = 1500; // Reduced from 2000ms to 1500ms for faster spawning
  const POINTS_PER_HIT = 10;

  // Start spawning prey when activated
  useEffect(() => {
    if (isActive && gamePhase === 'waiting') {
      setGamePhase('spawning');
      startSpawning();
    }
  }, [isActive]);

  // Spawn prey at intervals
  const startSpawning = () => {
    let spawnCount = 0;
    
    const spawnInterval = setInterval(() => {
      if (spawnCount >= TOTAL_PREY) {
        clearInterval(spawnInterval);
        // Check if game should end after all prey are spawned
        setTimeout(() => {
          if (activePrey.length === 0) {
            endGame();
          }
        }, 8000); // Wait 8 seconds for last prey to finish
        return;
      }

      // Create new prey
      const preyType = preyTypes[Math.floor(Math.random() * preyTypes.length)];
      const newPrey = {
        id: `prey-${Date.now()}-${spawnCount}`,
        type: preyType,
        startDelay: 500 + Math.random() * 1000 // Random delay 0.5-1.5s
      };

      console.log('Spawning new prey:', newPrey);
      setActivePrey(prev => {
        const updated = [...prev, newPrey];
        console.log('Active prey count:', updated.length);
        return updated;
      });
      setPreyCounter(prev => prev + 1);
      spawnCount++;
    }, SPAWN_INTERVAL);
  };

  // Handle when prey is shot
  const handlePreyShot = (preyType, takesLife = false) => {
    if (takesLife) {
      // Dangerous prey - pause game and trigger life loss
      console.log(`Dangerous prey ${preyType} shot! Taking a life.`);
      setIsPaused(true);
      setGamePhase('paused');

      if (onLifeLost) {
        onLifeLost(preyType); // Notify parent about life loss with prey type
      }

      // Game will resume after life loss animation completes
      return;
    }

    // Safe prey - give points only
    const newScore = score + POINTS_PER_HIT;

    setScore(newScore);

    if (onScoreUpdate) {
      onScoreUpdate({
        score: newScore,
        preyType: preyType // Pass prey type for tracking
      });
    }
  };

  // Handle when prey completes (shot or escaped)
  const handlePreyComplete = (preyId, wasShot) => {
    setActivePrey(prev => prev.filter(prey => prey.id !== preyId));
    
    // Check if game should end
    setTimeout(() => {
      if (activePrey.length <= 1 && gamePhase === 'spawning' && preyCounter >= TOTAL_PREY) {
        endGame();
      }
    }, 100);
  };

  // Resume game after life loss animation
  const resumeGame = () => {
    console.log('Resuming game after life loss');
    setIsPaused(false);
    setGamePhase('spawning');
  };

  // End the game
  const endGame = () => {
    setGamePhase('complete');
    if (onGameComplete) {
      onGameComplete({
        score,
        totalPrey: TOTAL_PREY,
        hitPrey: Math.floor(score / POINTS_PER_HIT)
      });
    }
  };

  // Expose API to parent component
  useImperativeHandle(ref, () => ({
    resumeGame
  }));

  if (!isActive) {
    return null;
  }

  console.log('PixelPreyManager render:', {
    isActive,
    gamePhase,
    activePrey: activePrey.length,
    preyCounter
  });

  return (
    <div className="absolute inset-0 z-50"> {/* Higher z-index for proper layering */}
      {activePrey.map((prey) => (
        <PixelPrey
          key={prey.id}
          type={prey.type}
          startDelay={prey.startDelay}
          onShot={(type, takesLife) => handlePreyShot(type, takesLife)}
          onComplete={(type, wasShot) => handlePreyComplete(prey.id, wasShot)}
          isGameActive={gamePhase === 'spawning' && !isPaused}
        />
      ))}
    </div>
  );
});

export default PixelPreyManager;
